"use client";
import { useEffect, useState } from "react";
import {
  Banner,
  SocialIcon,
  TournamentCategory,
} from "@/app/types/CommonComponent.types";
import withSidebar from "./hoc/withSidebar";
import Loader from "./components/common/Loader";
import api from "./utils/axiosInstance";
import { API_ENDPOINTS } from "./constants/apiEndpoints";
import TournamentSection from "./components/tournamentSection";
import SliderBanner from "./components/banner";
import { useSearchParams } from "next/navigation";
import { useSidebar } from "./context/SidebarContext";
import GamyDayPartners from "./components/gamyDayPartners";
import Link from "next/link";

const Home = () => {
  const { isExpanded } = useSidebar();
  const [tournamentsData, setTournamentsData] = useState<TournamentCategory[]>(
    []
  );

  const [banners, setBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTournamentId, setSelectedTournamentId] = useState<
    string | null
  >(null);
  const [socialIcons, setSocialIcons] = useState<SocialIcon[]>([]);
  const searchParams = useSearchParams();

  useEffect(() => {
    const tournamentId = searchParams.get("tournament_id");
    if (tournamentId) {
      setSelectedTournamentId(tournamentId);
    }
  }, [searchParams]);

  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [tournamentsResponse, bannersResponse] = await Promise.all([
        api.get<{ data: TournamentCategory[] }>(
          API_ENDPOINTS.GET_ALL_TOURNAMENTS
        ),
        api.get<Banner[]>(API_ENDPOINTS.GET_BANNERS),
      ]);

      if (tournamentsResponse.status === 200) {
        setTournamentsData(tournamentsResponse.data.data);
      }

      if (bannersResponse.status === 200) {
        setBanners(bannersResponse.data);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSocialIcons = async () => {
    try {
      const response = await api.get(API_ENDPOINTS.SOCIAL_ICONS);
      if (response.status === 200) {
        setSocialIcons(response?.data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchInitialData();
    fetchSocialIcons();
  }, []);

  if (error) {
    return <div className="p-8 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="px-8 py-5 flex flex-col gap-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-[50vh]">
          <Loader />
        </div>
      ) : (
        <>
          <div className="flex gap-10">
            <div className="w-[300px] bg-gradient-to-b from-[#121f28] to-[#1a2c38] px-5 flex flex-col justify-between pb-5 rounded border border-[#c9ff88]">
              <h2 className="text-xl font-bold text-white my-5 flex flex-col">
                <span className="text-5xl">GamyDay</span>
                <span className="text-3xl pl-1 mt-1 ">Earn Every Day!</span>
                <span className="mt-4 pl-1">Play ➔ Earn ➔ Repeat</span>
              </h2>
              <div className="">
                <h3 className="text-lg font-semibold text-white mb-2.5">
                  Social links
                </h3>
                {socialIcons && (
                  <div className="flex gap-4 flex-wrap">
                    {socialIcons.map((icon, index) => (
                      <Link
                        href={icon.link}
                        key={index}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="cursor-pointer bg-slate-400 bg-opacity-30 p-2 rounded"
                      >
                        <img src={icon.image} width={20} height={20} alt="" />
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="flex-1">
              {banners.length > 0 && (
                <div
                  className={`${
                    isExpanded
                      ? "w-[calc(100vw-760px)]"
                      : "w-[calc(100vw-540px)]"
                  }`}
                >
                  <SliderBanner banners={banners} />
                </div>
              )}
              <GamyDayPartners />
            </div>
          </div>

          {tournamentsData.map((tournamentCategory, index) => (
            <TournamentSection
              key={`${tournamentCategory.type}-${index}`}
              tournaments={tournamentCategory.tournaments}
              sectionTitle={tournamentCategory.type}
              selectedTournamentId={selectedTournamentId}
            />
          ))}
        </>
      )}
    </div>
  );
};

export default withSidebar(Home);

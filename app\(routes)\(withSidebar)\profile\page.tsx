"use client";
import React, { useState } from "react";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";
import Image from "next/image";
import { PencilSquareIcon } from "@heroicons/react/24/outline";
import { Modal } from "@/app/components/modal";
import UpdateProfileForm from "@/app/components/updateProfileForm";
import withAuth from "@/app/utils/withAuth";

const ProfilePage = () => {
  const [openProfileFormModal, setOpenProfileFormModal] = useState(false);
  const user = useSelector((state: RootState) => state.user);
  const handleProfileUpdate = () => {
    setOpenProfileFormModal(true);
  };

  return (
    <div className="flex justify-center items-center w-full mt-20">
      <div className="py-8 bg-[#141517] rounded-[30px] border border-[#707070] w-[800px] h-fit">
        <div className="flex flex-col gap-4 items-center justify-center relative mr-28">
          <Image
            alt=""
            src="/icons/avatar.png"
            width={80}
            height={80}
            className="rounded-full bg-gray-50"
          />
          <span className="text-white font-bold text-2xl">{user.name}</span>
          <button
            className="absolute top-6 right-16"
            onClick={handleProfileUpdate}
          >
            <PencilSquareIcon
              aria-hidden="true"
              className="h-6 w-6 shrink-0 font-semibold text-white ml-3 cursor-pointer hover:text-red-500 transition-all"
            />
          </button>
        </div>
        <div className="w-[500px] mx-auto mt-8 space-y-2">
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">Email</span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.email}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">Phone</span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.phone}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">
                PAN Number
              </span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.pan_number}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-8">
            <div>
              <span className="font-semibold text-base text-white">UPI ID</span>
            </div>
            <div>
              <span className="font-semibold text-base text-white">
                {user.upi_id}
              </span>
            </div>
          </div>
        </div>
      </div>
      <Modal
        modalOpen={openProfileFormModal}
        handleModalOpen={() => setOpenProfileFormModal(false)}
      >
        <UpdateProfileForm handleModal={setOpenProfileFormModal} user={user} />
      </Modal>
    </div>
  );
};

export default withAuth(ProfilePage);

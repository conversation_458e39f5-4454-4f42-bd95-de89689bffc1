import { Fragment } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { ModalProps } from "@/app/types/CommonComponent.types";
import { ArrowPathIcon } from "@heroicons/react/24/outline";

export const Modal = ({ children, modalOpen, handleModalOpen, showRefreshButton = false, onRefresh }: ModalProps) => {
  return (
    <Transition.Root show={modalOpen} as={Fragment}>
      <Dialog as="div" className="relative z-[999]" onClose={() => {}}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-[9999999] w-screen overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-[#242424] px-16 py-10 text-left shadow-xl transition-all">
                {/* Refresh button - upper left corner */}
                {showRefreshButton && onRefresh && (
                  <div className="absolute left-0 top-0 pl-4 pt-4">
                    <button
                      type="button"
                      className="bg-gray-700 hover:bg-gray-600 text-white rounded-full h-9 w-9 font-semibold cursor-pointer transition-colors duration-200 flex items-center justify-center"
                      onClick={onRefresh}
                      title="Refresh game details"
                    >
                      <ArrowPathIcon className="h-4 w-4" />
                    </button>
                  </div>
                )}

                {/* Close button - upper right corner */}
                <div className="absolute right-0 top-0 pr-4 pt-4">
                  <button
                    type="button"
                    className="bg-red-500 text-[#070b28] rounded-full h-9 w-9 font-semibold cursor-pointer"
                    onClick={() => handleModalOpen(false)}
                  >
                    ✕
                  </button>
                </div>
                <div className="text-left">{children}</div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

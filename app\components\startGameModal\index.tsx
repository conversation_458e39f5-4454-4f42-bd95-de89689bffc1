import {
  StartGameModalProps,
  UpdateInGameNameResponse,
} from "@/app/types/CommonComponent.types";
import Link from "next/link";
import UpdateInGameNameForm from "../updateInGameNameForm";
import { useState, useEffect } from "react";
const StartGameModal: React.FC<StartGameModalProps> = ({
  game_link,
  room_password,
  room_id,
  bookingId,
  tournamentName,
  inGameName,
  showUpdateInGameNameForm = false,
  onSuccess,
  createdBy,
}) => {
  const [updatedInGameName, setUpdatedInGameName] = useState(inGameName);
  const [updatedRoomId, setUpdatedRoomId] = useState(room_id);
  const [updatedRoomPassword, setUpdatedRoomPassword] = useState(room_password);
  const [updatedGameLink, setUpdatedGameLink] = useState(game_link);

  // Sync local state with props when they change (important for refresh functionality)
  useEffect(() => {
    setUpdatedRoomId(room_id);
  }, [room_id]);

  useEffect(() => {
    setUpdatedRoomPassword(room_password);
  }, [room_password]);

  useEffect(() => {
    setUpdatedGameLink(game_link);
  }, [game_link]);

  useEffect(() => {
    setUpdatedInGameName(inGameName);
  }, [inGameName]);

  const onInGameNameUpdate = (data: UpdateInGameNameResponse) => {
    if (onSuccess && typeof onSuccess === "function") {
      onSuccess();
    }
    setUpdatedInGameName(data.in_game_name);
    setUpdatedRoomId(data.room_id);
    setUpdatedRoomPassword(data.room_password);
    setUpdatedGameLink(data.game_link);
  };
  return (
    <div className="px-4">
      {/* Header with title */}
      <div className="mb-6">
        <h3 className="text-3xl font-bold text-white">
          Game Joining Details
        </h3>
      </div>

      {/* Main content layout */}
      <div className="flex gap-6">
        {/* Left side - Host ki Image */}
        {createdBy && (
          <div className="flex-shrink-0 w-[180px]">
            <div className="flex flex-col items-center space-y-3">
              <div className="w-[120px] h-[120px] relative">
                <img
                  src={createdBy.image || "/icons/youtube.svg"}
                  alt={createdBy.name}
                  className="object-cover rounded-[10px] w-full h-full"
                />
              </div>
              <Link
                href={createdBy.youtube_link || ""}
                target="_blank"
                className="flex flex-col items-center w-full bg-[#c9ff88] text-[#070b28] px-3 py-2 rounded-[10px] text-sm font-semibold hover:bg-[#a7f04e] transition-colors"
              >
                <span>Created by:</span>
                <span className="text-center leading-tight">{createdBy.name}</span>
              </Link>
            </div>
          </div>
        )}

        {/* Right side - Game Details */}
        <div className="flex-1">
          {!updatedInGameName && showUpdateInGameNameForm && (
            <div className="mb-6">
              <UpdateInGameNameForm
                bookingId={bookingId as string}
                onSuccess={onInGameNameUpdate}
                tournamentName={tournamentName as string}
              />
            </div>
          )}
          <div className="mb-6 space-y-1">
            {showUpdateInGameNameForm && (
              <div className="grid grid-cols-2 gap-x-8">
                <div>
                  <span className="font-semibold text-lg text-white">
                    In Game Name
                  </span>
                </div>
                <div>
                  <span className="font-medium text-lg text-white">
                    {updatedInGameName}
                  </span>
                </div>
              </div>
            )}
            <div className="grid grid-cols-2 gap-x-8">
              <div>
                <span className="font-semibold text-lg text-white">Room ID</span>
              </div>
              <div>
                <span className="font-medium text-lg text-white">
                  {updatedRoomId}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-x-8">
              <div>
                <span className="font-semibold text-lg text-white">
                  Room Password
                </span>
              </div>
              <div>
                <span className="font-medium text-lg text-white">
                  {updatedRoomPassword}
                </span>
              </div>
            </div>
          </div>

          <div className="mb-3">
            <p className="text-sm text-red">
              <span className="font-semibold">Notes:</span> Joining details will be
              available before the start of the tournament.
            </p>
          </div>

          <div className="flex gap-4">
            <Link
              href={updatedGameLink ?? ""}
              target="_blank"
              className={`flex justify-center w-full rounded-md bg-red-600 px-4 py-2 text-base font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 ${
                !updatedGameLink ? "cursor-not-allowed opacity-70" : ""
              }`}
              onClick={(e) => {
                if (!updatedGameLink) {
                  e.preventDefault();
                }
              }}
            >
              Click to Watch Live
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartGameModal;

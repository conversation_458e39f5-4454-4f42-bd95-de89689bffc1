"use client";
import { useForm } from "react-hook-form";
import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/redux/store";
import { setUser } from "@/redux/slices/userSlice";
import { UpdateProfileFormProps } from "@/app/types/CommonComponent.types";
import {
  UpdateProfileFormData,
  UpdateProfileFormSchema,
} from "@/app/schema/updateProfileSchema";

const UpdateProfileForm: React.FC<UpdateProfileFormProps> = ({
  handleModal,
  user,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [apiError, setApiError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<UpdateProfileFormData>({
    resolver: zodResolver(UpdateProfileFormSchema),
  });

  useEffect(() => {
    setValue("name", user.name);
    setValue("upi_id", user.upi_id);
    setValue("pan_number", user.pan_number);
  }, [setValue]);

  const onSubmit = async (data: UpdateProfileFormData) => {
    setApiError(null);
    try {
      const res = await api.patch(API_ENDPOINTS.GET_USER, data);
      if (res.status === 200) {
        dispatch(setUser(res?.data?.data));
        toast.success("Profile updated sucessfully!");
        handleModal(false);
      }
    } catch (error: any) {
      setApiError(
        error?.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  return (
    <div className="w-[450px]">
      <h2 className="text-2xl font-semibold text-white mb-6 text-center">
        Update Your Profile Details
      </h2>

      {apiError && (
        <div className="mb-4 p-2 bg-red-500 text-white rounded">{apiError}</div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            Name
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your name"
            {...register("name")}
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
          )}
        </div>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            UPI ID
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your UPI ID"
            {...register("upi_id")}
          />
          {errors.upi_id && (
            <p className="text-red-500 text-sm mt-1">{errors.upi_id.message}</p>
          )}
        </div>
        <div className="mb-4">
          <label className="block font-medium leading-6 text-white mb-2">
            PAN Number
          </label>
          <input
            type="text"
            className="w-full p-2 bg-gray-100 rounded-lg outline-none focus:ring-2 focus:ring-indigo-500 text-black"
            placeholder="Enter your PAN Number"
            {...register("pan_number")}
          />
          {errors.pan_number && (
            <p className="text-red-500 text-sm mt-1">
              {errors.pan_number.message}
            </p>
          )}
        </div>

        <div className="mt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex w-full justify-center rounded-md bg-red-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-500 disabled:cursor-not-allowed disabled:opacity-70"
          >
            {isSubmitting ? "Updating..." : "Update"}
          </button>
        </div>
      </form>
    </div>
  );
};
export default UpdateProfileForm;
